# MatchScoringScreen Refactoring Plan

## Current Problem
The `MatchScoringScreen.tsx` file is **2,592 lines** and contains everything:
- UI state management
- Cricket scoring logic  
- Match state updates
- Complex UI rendering
- Player selection logic
- Wicket handling
- Over completion logic

This makes it:
- **Hard to test** - everything is coupled
- **Hard to maintain** - changes affect multiple concerns
- **Hard to understand** - massive cognitive load
- **Hard to debug** - complex interactions

## Refactoring Strategy

### Phase 1: Extract Cricket Logic Modules 🏏

#### 1.1 Create `src/services/cricketScoring.ts`
```typescript
// Pure functions for cricket scoring logic
export const calculateOverProgress = (ballsInCurrentOver: number) => {...}
export const shouldRotateStrike = (runsScored: number) => {...}
export const updatePlayerStats = (player: Player, outcome: BallOutcome) => {...}
export const checkInningsEnd = (match: Match, innings: Innings) => {...}
```

#### 1.2 Create `src/services/matchEngine.ts`
```typescript
// Core match state management
export class MatchEngine {
  updateScore(outcome: BallOutcome): MatchUpdate
  handleWicket(wicketType: WicketType): MatchUpdate  
  completeOver(): MatchUpdate
  checkTargetAchieved(): boolean
}
```

#### 1.3 Create `src/services/playerManager.ts`
```typescript
// Player selection and rotation logic
export const getAvailableBatsmen = (match: Match) => {...}
export const getAvailableBowlers = (match: Match) => {...}
export const rotateStrike = (match: Match) => {...}
```

### Phase 2: Extract Custom Hooks 🪝

#### 2.1 Create `src/hooks/useMatchState.ts`
```typescript
export const useMatchState = (matchId: string) => {
  // Handle match loading, saving, state management
  return { match, updateMatch, isLoading, error }
}
```

#### 2.2 Create `src/hooks/usePlayerSelection.ts`
```typescript
export const usePlayerSelection = (match: Match) => {
  // Handle batsman/bowler selection logic
  return { 
    selectedStriker, 
    selectedNonStriker, 
    selectedBowler,
    selectStriker,
    selectBowler,
    confirmSelection
  }
}
```

#### 2.3 Create `src/hooks/useCricketScoring.ts`
```typescript
export const useCricketScoring = (match: Match) => {
  // Handle ball-by-ball scoring
  return {
    playBall,
    handleWicket,
    completeOver,
    isInningsComplete,
    isMatchComplete
  }
}
```

### Phase 3: Extract UI Components 🎨

#### 3.1 Create `src/components/scoring/ScoringControls.tsx`
```typescript
// Ball outcome buttons (0,1,2,3,4,6,Wide,NoBall,Wicket)
```

#### 3.2 Create `src/components/scoring/PlayerSelection.tsx`
```typescript
// Batsman and bowler selection UI
```

#### 3.3 Create `src/components/scoring/ScoreDisplay.tsx`
```typescript
// Current score, overs, player stats display
```

#### 3.4 Create `src/components/scoring/WicketModal.tsx`
```typescript
// Wicket type selection modal
```

### Phase 4: Simplified MatchScoringScreen 📱

After refactoring, the main component becomes:

```typescript
export default function MatchScoringScreen() {
  const { matchId } = useRoute().params;
  
  // Custom hooks handle the complexity
  const { match, updateMatch, isLoading } = useMatchState(matchId);
  const { playBall, handleWicket } = useCricketScoring(match);
  const playerSelection = usePlayerSelection(match);
  
  if (isLoading) return <LoadingScreen />;
  if (!match) return <ErrorScreen />;
  
  return (
    <ScrollView>
      <ScoreDisplay match={match} />
      <PlayerSelection {...playerSelection} />
      <ScoringControls onPlayBall={playBall} onWicket={handleWicket} />
    </ScrollView>
  );
}
```

## Benefits After Refactoring

### 🧪 **Easier Testing**
- **Unit test** cricket logic separately
- **Test hooks** in isolation  
- **Test components** with mock data
- **Integration tests** for specific flows

### 🔧 **Better Maintainability**
- **Single responsibility** for each module
- **Clear boundaries** between concerns
- **Easier debugging** - smaller scope
- **Reusable logic** across screens

### 📈 **Improved Performance**
- **Smaller re-renders** with focused components
- **Memoization opportunities** in pure functions
- **Lazy loading** of complex logic

### 🏗️ **Scalability**
- **Add new features** without touching core logic
- **Extend cricket rules** in dedicated modules
- **Reuse scoring engine** for different game formats

## Implementation Order

1. ✅ **Start with pure functions** (cricketScoring.ts)
2. ✅ **Extract match engine** (stateful but isolated)  
3. ✅ **Create custom hooks** (integrate with React)
4. ✅ **Build UI components** (focused and testable)
5. ✅ **Refactor main screen** (orchestration only)
6. ✅ **Update tests** (much easier now!)

This approach will transform the monolithic 2,592-line component into a clean, testable, maintainable architecture! 🚀