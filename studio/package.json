{"name": "gullyscoreexpo", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "expo": "53.0.11", "expo-status-bar": "~2.2.3", "lucide-react-native": "^0.511.0", "prop-types": "^15.8.1", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-picker-select": "^9.3.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zod": "^3.25.1", "react-native-gesture-handler": "~2.24.0", "expo-dev-client": "~5.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "babel-jest": "^29.7.0", "babel-preset-expo": "~13.0.0", "jest": "^29.7.0", "jest-expo": "^53.0.5", "react-test-renderer": "^19.0.0", "ts-jest": "^29.3.4", "typescript": "~5.8.3"}, "private": true}