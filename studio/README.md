# GullyScore - React Native Edition

This is the React Native version of GullyScore, a cricket scoring application. This repository contains the mobile application developed using Expo.

## Prerequisites

Before you begin, ensure you have the following installed:

*   **Node.js:** (LTS version recommended). You can download it from [nodejs.org](https://nodejs.org/).
*   **npm** or **Yarn:** These are package managers for Node.js. npm is included with Node.js.
*   **Watchman:** (Recommended for macOS users for better performance with Metro bundler). Install via Homebrew: `brew install watchman`
*   **Expo Go App:** Install the Expo Go app on your iOS or Android physical device if you plan to test on it. You can find it in the App Store (iOS) or Google Play Store (Android).
*   **Git:** For cloning the repository.

## Getting Started

> **Branch Information:**
> 
> All development and usage should be done on the `main` branch.  
> After cloning, you will be on `main` by default. If not, switch to it:
> ```bash
> git checkout main
> ```

Follow these steps to get the project running on your local machine:

1.  **Clone the Repository:**
    ```bash
    git clone https://github.com/velchai25/studio.git
    cd studio
    ```

2.  **Install Dependencies:**
    Install the project dependencies using npm or yarn:
    ```bash
    npm install
    # OR
    yarn install
    ```

3.  **Running the Application:**

    *   **Start the Metro Bundler:**
        ```bash
        npx expo start
        ```
        This command will start the Metro Bundler and provide you with a QR code and options to run the app.

    *   **Run on Web:**
        *   In the terminal where Metro Bundler is running, press `w`. This will attempt to open the app in your default web browser.
        *   You might need to install web-specific dependencies if prompted: `npx expo install react-native-web react-dom @expo/metro-runtime` (Metro should guide you).

    *   **Run on iOS Simulator:**
        *   In the terminal, press `i`. This will attempt to open the app in the iOS Simulator (requires Xcode to be installed and configured on macOS).

    *   **Run on Android Emulator/Device:**
        *   In the terminal, press `a`. This will attempt to open the app on a connected Android device or a running Android Emulator (requires Android Studio and emulator setup).

    *   **Run on a Physical iOS/Android Device:**
        *   Open the Expo Go app on your device.
        *   Scan the QR code displayed in the terminal by Metro Bundler. Ensure your device and computer are on the same Wi-Fi network.

## Project Structure (Inside `src/`)

*   `screens/`: Contains the main screen components of the application.
*   `components/`: Reusable UI components.
*   `navigation/`: Navigation setup using React Navigation.
*   `types/`: TypeScript type definitions, especially for match data.
*   `utils/`: Utility functions, like data storage (`AsyncStorage`).

## Data Persistence

Currently, match data is persisted locally on the device using `@react-native-async-storage/async-storage`.

## Future (Firebase Integration - Placeholder)

*(This section can be updated if/when Firebase is integrated for backend data storage and synchronization, replacing or augmenting AsyncStorage.)* 