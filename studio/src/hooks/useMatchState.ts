import { useState, useEffect, useCallback } from 'react';
import { Match } from '../types/matchTypes';
import { loadMatch, saveMatch } from '../utils/storage';

export interface UseMatchStateReturn {
  match: Match | null;
  isLoading: boolean;
  error: string | null;
  updateMatch: (updatedMatch: Match) => Promise<boolean>;
  refreshMatch: () => Promise<void>;
}

/**
 * Custom hook for managing match state
 * Handles loading, saving, and updating match data
 */
export const useMatchState = (matchId: string): UseMatchStateReturn => {
  const [match, setMatch] = useState<Match | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load match data from storage
   */
  const loadMatchData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const matchData = await loadMatch(matchId);
      if (matchData) {
        setMatch(matchData);
      } else {
        setError('Match not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load match');
    } finally {
      setIsLoading(false);
    }
  }, [matchId]);

  /**
   * Update and save match data
   */
  const updateMatch = useCallback(async (updatedMatch: Match): Promise<boolean> => {
    try {
      const success = await saveMatch({
        ...updatedMatch,
        updatedAt: Date.now(),
      });
      
      if (success) {
        setMatch(updatedMatch);
        return true;
      } else {
        setError('Failed to save match');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save match');
      return false;
    }
  }, []);

  /**
   * Refresh match data (useful after external updates)
   */
  const refreshMatch = useCallback(async () => {
    await loadMatchData();
  }, [loadMatchData]);

  // Load match data on mount or when matchId changes
  useEffect(() => {
    loadMatchData();
  }, [loadMatchData]);

  return {
    match,
    isLoading,
    error,
    updateMatch,
    refreshMatch,
  };
};