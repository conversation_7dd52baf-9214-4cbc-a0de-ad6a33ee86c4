import { useCallback } from 'react';
import { Match, BallOutcome, Player } from '../types/matchTypes';
import { MatchEngine, MatchUpdate } from '../services/matchEngine';
import { getAvailableBatsmen, getAvailableBowlers } from '../services/cricketScoring';

export interface UseCricketScoringReturn {
  playBall: (outcome: BallOutcome) => Promise<MatchUpdate | null>;
  setNewBatsman: (player: Player) => Promise<Match | null>;
  setNewBowler: (player: Player) => Promise<Match | null>;
  getAvailableBatsmenForSelection: () => Player[];
  getAvailableBowlersForSelection: () => Player[];
  getCurrentInnings: () => any;
  isMatchActive: boolean;
  canPlayBall: boolean;
}

/**
 * Custom hook for cricket scoring operations
 * Integrates the match engine with React state management
 */
export const useCricketScoring = (
  match: Match | null,
  updateMatch: (match: Match) => Promise<boolean>
): UseCricketScoringReturn => {

  /**
   * Play a ball and update match state
   */
  const playBall = useCallback(async (outcome: BallOutcome): Promise<MatchUpdate | null> => {
    if (!match) return null;

    try {
      const matchUpdate = MatchEngine.playBall(match, outcome);
      const success = await updateMatch(matchUpdate.updatedMatch);
      
      if (success) {
        return matchUpdate;
      } else {
        throw new Error('Failed to save match update');
      }
    } catch (error) {
      console.error('Error playing ball:', error);
      return null;
    }
  }, [match, updateMatch]);

  /**
   * Set a new batsman after wicket
   */
  const setNewBatsman = useCallback(async (player: Player): Promise<Match | null> => {
    if (!match) return null;

    try {
      const updatedMatch = MatchEngine.setNewBatsman(match, player.id);
      const success = await updateMatch(updatedMatch);
      
      if (success) {
        return updatedMatch;
      } else {
        throw new Error('Failed to save new batsman');
      }
    } catch (error) {
      console.error('Error setting new batsman:', error);
      return null;
    }
  }, [match, updateMatch]);

  /**
   * Set a new bowler for next over
   */
  const setNewBowler = useCallback(async (player: Player): Promise<Match | null> => {
    if (!match) return null;

    try {
      const updatedMatch = MatchEngine.setNewBowler(match, player.id);
      const success = await updateMatch(updatedMatch);
      
      if (success) {
        return updatedMatch;
      } else {
        throw new Error('Failed to save new bowler');
      }
    } catch (error) {
      console.error('Error setting new bowler:', error);
      return null;
    }
  }, [match, updateMatch]);

  /**
   * Get available batsmen for selection
   */
  const getAvailableBatsmenForSelection = useCallback((): Player[] => {
    if (!match) return [];

    const currentInnings = getCurrentInnings();
    if (!currentInnings) return [];

    const battingTeamPlayers = currentInnings.battingTeamName === match.teamAName 
      ? match.teamAPlayers 
      : match.teamBPlayers;

    return getAvailableBatsmen(
      battingTeamPlayers,
      currentInnings.strikerId,
      currentInnings.nonStrikerId
    );
  }, [match]);

  /**
   * Get available bowlers for selection
   */
  const getAvailableBowlersForSelection = useCallback((): Player[] => {
    if (!match) return [];

    const currentInnings = getCurrentInnings();
    if (!currentInnings) return [];

    const bowlingTeamPlayers = currentInnings.bowlingTeamName === match.teamAName 
      ? match.teamAPlayers 
      : match.teamBPlayers;

    return getAvailableBowlers(bowlingTeamPlayers);
  }, [match]);

  /**
   * Get current innings data
   */
  const getCurrentInnings = useCallback(() => {
    if (!match) return null;
    
    return match.status === 'live_inning1' 
      ? match.firstInnings 
      : match.secondInnings;
  }, [match]);

  /**
   * Check if match is in an active scoring state
   */
  const isMatchActive = Boolean(
    match && 
    (match.status === 'live_inning1' || match.status === 'live_inning2') &&
    match.status !== 'completed'
  );

  /**
   * Check if we can play a ball (all players selected and match active)
   */
  const canPlayBall = useCallback((): boolean => {
    if (!isMatchActive) return false;
    
    const currentInnings = getCurrentInnings();
    if (!currentInnings) return false;

    // Must have striker and bowler selected
    const hasStriker = Boolean(currentInnings.strikerId);
    const hasBowler = Boolean(currentInnings.currentBowlerId);
    
    // For normal cricket, must also have non-striker (unless last man standing)
    let hasNonStriker = true;
    if (!match?.lastManStanding || 
        (match.lastManStanding && currentInnings.wickets < match.playersPerTeam - 1)) {
      hasNonStriker = Boolean(currentInnings.nonStrikerId);
    }

    return hasStriker && hasBowler && hasNonStriker;
  }, [isMatchActive, match, getCurrentInnings]);

  return {
    playBall,
    setNewBatsman,
    setNewBowler,
    getAvailableBatsmenForSelection,
    getAvailableBowlersForSelection,
    getCurrentInnings,
    isMatchActive,
    canPlayBall: canPlayBall(),
  };
};