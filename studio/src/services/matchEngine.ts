import { Match, BallOutcome } from '../types/matchTypes';
import {
  analyzeBallOutcome,
  updateBatsmanStats,
  updateBowlerStats,
  checkMaidenOver,
  getMaxWicketsForInnings,
  shouldInningsEnd,
  isTargetAchieved,
  generateMatchResult
} from './cricketScoring';

export interface MatchUpdate {
  updatedMatch: Match;
  shouldSelectNewBatsman: boolean;
  shouldSelectNewBowler: boolean;
  isInningsComplete: boolean;
  isMatchComplete: boolean;
  alerts: string[];
}

/**
 * Match Engine - Handles all match state updates
 * This class encapsulates the complex cricket match logic
 */
export class MatchEngine {
  /**
   * Process a ball and update match state
   */
  static playBall(match: Match, outcome: BallOutcome): MatchUpdate {
    const updatedMatch = JSON.parse(JSON.stringify(match)) as Match;
    const alerts: string[] = [];
    
    // Get current innings
    const currentInnings = updatedMatch.status === 'live_inning1' 
      ? updatedMatch.firstInnings 
      : updatedMatch.secondInnings;
    
    if (!currentInnings) {
      throw new Error('No active innings found');
    }

    // Analyze the ball outcome
    const scoringResult = analyzeBallOutcome(outcome);
    
    // Update innings score and timeline
    currentInnings.score += scoringResult.runsScored;
    currentInnings.timeline.push(outcome);
    currentInnings.currentOverHistory.push(MatchEngine.getOverHistoryEntry(outcome));
    
    // Update maiden over tracking
    if (!scoringResult.isExtra || outcome === BallOutcome.NO_BALL) {
      currentInnings.runsConcededThisOverMaiden += scoringResult.runsScored;
    }

    // Get current players
    const batterTeamKey = currentInnings.battingTeamName === updatedMatch.teamAName ? 'teamAPlayers' : 'teamBPlayers';
    const bowlerTeamKey = currentInnings.bowlingTeamName === updatedMatch.teamAName ? 'teamAPlayers' : 'teamBPlayers';
    
    const striker = updatedMatch[batterTeamKey].find(p => p.id === currentInnings.strikerId);
    const bowler = updatedMatch[bowlerTeamKey].find(p => p.id === currentInnings.currentBowlerId);
    
    if (!striker || !bowler) {
      throw new Error('Striker or bowler not found');
    }

    // Update player stats
    striker.battingStats = updateBatsmanStats(
      striker.battingStats, 
      outcome, 
      scoringResult.isLegalDelivery, 
      scoringResult.runsScored
    );
    
    bowler.bowlingStats = updateBowlerStats(
      bowler.bowlingStats, 
      outcome, 
      scoringResult.isLegalDelivery, 
      scoringResult.runsScored
    );

    // Handle wicket
    let shouldSelectNewBatsman = false;
    if (scoringResult.isWicket) {
      currentInnings.wickets += 1;
      const result = MatchEngine.handleWicket(updatedMatch, currentInnings, batterTeamKey);
      shouldSelectNewBatsman = result.shouldSelectNewBatsman;
    }

    // Handle strike rotation for odd runs
    if (scoringResult.shouldRotateStrike) {
      MatchEngine.rotateStrike(currentInnings);
    }

    // Handle legal delivery
    let shouldSelectNewBowler = false;
    let isOverComplete = false;
    if (scoringResult.isLegalDelivery) {
      currentInnings.ballsInCurrentOver += 1;
      
      // Check for over completion
      if (currentInnings.ballsInCurrentOver >= 6) {
        isOverComplete = true;
        shouldSelectNewBowler = true;
        
        // Complete the over
        currentInnings.oversCompleted += 1;
        currentInnings.ballsInCurrentOver = 0;
        
        // Rotate strike at end of over
        MatchEngine.rotateStrike(currentInnings);
        
        // Check for maiden over
        if (checkMaidenOver(currentInnings.runsConcededThisOverMaiden)) {
          bowler.bowlingStats.maidensBowled += 1;
          alerts.push(`Maiden Over! ${bowler.name} bowled a maiden!`);
        }
        
        // Reset maiden tracking
        currentInnings.runsConcededThisOverMaiden = 0;
        
        alerts.push(`Over Complete! Over ${currentInnings.oversCompleted} finished by ${bowler.name}.`);
      }
    }

    // Check innings/match end conditions
    const maxWickets = getMaxWicketsForInnings(updatedMatch.playersPerTeam, updatedMatch.lastManStanding);
    let isInningsComplete = false;
    let isMatchComplete = false;

    // Check target achieved in second innings
    if (updatedMatch.status === 'live_inning2' && 
        updatedMatch.secondInnings && 
        isTargetAchieved(currentInnings.score, updatedMatch.secondInnings.target)) {
      
      isMatchComplete = true;
      isInningsComplete = true;
      updatedMatch.status = 'completed';
      updatedMatch.matchWinnerTeamName = currentInnings.battingTeamName;
      updatedMatch.resultDescription = generateMatchResult(
        updatedMatch,
        currentInnings.battingTeamName,
        true,
        currentInnings.wickets
      );
      
    } else if (shouldInningsEnd(currentInnings.wickets, currentInnings.oversCompleted, updatedMatch.oversPerInnings, maxWickets)) {
      
      isInningsComplete = true;
      
      if (updatedMatch.status === 'live_inning1') {
        // Start second innings
        alerts.push(`Innings Over! Score: ${currentInnings.score}/${currentInnings.wickets}. Target: ${currentInnings.score + 1}`);
        
        updatedMatch.status = 'live_inning2';
        updatedMatch.secondInnings = {
          battingTeamName: currentInnings.bowlingTeamName,
          bowlingTeamName: currentInnings.battingTeamName,
          score: 0,
          wickets: 0,
          oversCompleted: 0,
          ballsInCurrentOver: 0,
          currentOverHistory: [],
          runsConcededThisOverMaiden: 0,
          timeline: [],
          target: currentInnings.score + 1,
          strikerId: undefined,
          nonStrikerId: undefined,
          currentBowlerId: undefined,
        };
        
      } else if (updatedMatch.status === 'live_inning2') {
        // Match complete
        isMatchComplete = true;
        updatedMatch.status = 'completed';
        
        const firstInningsScore = updatedMatch.firstInnings?.score || 0;
        const secondInningsScore = currentInnings.score;
        
        if (secondInningsScore < firstInningsScore) {
          updatedMatch.matchWinnerTeamName = currentInnings.bowlingTeamName;
          updatedMatch.resultDescription = generateMatchResult(
            updatedMatch,
            currentInnings.bowlingTeamName,
            false,
            currentInnings.wickets
          );
        } else {
          updatedMatch.resultDescription = "Match Tied!";
        }
      }
    }

    // New batsman selection takes priority over new bowler selection
    if (shouldSelectNewBatsman) {
      shouldSelectNewBowler = false;
    }

    return {
      updatedMatch,
      shouldSelectNewBatsman,
      shouldSelectNewBowler: shouldSelectNewBowler && !isInningsComplete,
      isInningsComplete,
      isMatchComplete,
      alerts,
    };
  }

  /**
   * Handle wicket logic
   */
  private static handleWicket(
    match: Match,
    currentInnings: any,
    batterTeamKey: 'teamAPlayers' | 'teamBPlayers'
  ): { shouldSelectNewBatsman: boolean } {
    const maxWickets = getMaxWicketsForInnings(match.playersPerTeam, match.lastManStanding);

    if (currentInnings.wickets < maxWickets) {
      if (match.lastManStanding && currentInnings.wickets === match.playersPerTeam - 1) {
        // Last man standing scenario - only one batsman left
        const nonStrikerId = currentInnings.nonStrikerId;
        if (nonStrikerId) {
          const lastMan = match[batterTeamKey].find(p => p.id === nonStrikerId);
          if (lastMan && lastMan.battingStats.status !== 'out') {
            currentInnings.strikerId = nonStrikerId;
            currentInnings.nonStrikerId = null;
            return { shouldSelectNewBatsman: false };
          }
        }

        // Find any other available batsman
        const availableBatsman = match[batterTeamKey].find(
          p => p.battingStats.status !== 'out' && p.id !== currentInnings.strikerId
        );

        if (availableBatsman) {
          currentInnings.strikerId = availableBatsman.id;
          currentInnings.nonStrikerId = null;
          return { shouldSelectNewBatsman: false };
        }
      } else {
        // Normal wicket - need new batsman
        // Clear the striker position (the batsman who got out)
        currentInnings.strikerId = null;
        return { shouldSelectNewBatsman: true };
      }
    }

    return { shouldSelectNewBatsman: false };
  }

  /**
   * Rotate strike between batsmen
   */
  private static rotateStrike(currentInnings: any): void {
    if (!currentInnings.strikerId || !currentInnings.nonStrikerId) return;
    
    const tempStrikerId = currentInnings.strikerId;
    currentInnings.strikerId = currentInnings.nonStrikerId;
    currentInnings.nonStrikerId = tempStrikerId;
  }

  /**
   * Get over history entry for display
   */
  private static getOverHistoryEntry(outcome: BallOutcome): string {
    switch (outcome) {
      case BallOutcome.WICKET:
        return 'W';
      case BallOutcome.WIDE:
        return 'Wd';
      case BallOutcome.NO_BALL:
        return 'Nb';
      default:
        return String(outcome);
    }
  }

  /**
   * Set new batsman after wicket
   */
  static setNewBatsman(match: Match, newBatsmanId: string): Match {
    const updatedMatch = JSON.parse(JSON.stringify(match)) as Match;
    const currentInnings = updatedMatch.status === 'live_inning1' 
      ? updatedMatch.firstInnings 
      : updatedMatch.secondInnings;
    
    if (currentInnings) {
      currentInnings.strikerId = newBatsmanId;
    }
    
    return updatedMatch;
  }

  /**
   * Set new bowler for next over
   */
  static setNewBowler(match: Match, newBowlerId: string): Match {
    const updatedMatch = JSON.parse(JSON.stringify(match)) as Match;
    const currentInnings = updatedMatch.status === 'live_inning1' 
      ? updatedMatch.firstInnings 
      : updatedMatch.secondInnings;
    
    if (currentInnings) {
      currentInnings.currentBowlerId = newBowlerId;
      currentInnings.currentOverHistory = []; // Reset over history for new bowler
    }
    
    return updatedMatch;
  }
}