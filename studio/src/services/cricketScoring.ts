import { Player, Match, BallOutcome, PlayerBattingStats, PlayerBowlingStats } from '../types/matchTypes';

/**
 * Pure cricket scoring utility functions
 * These functions handle cricket-specific logic without side effects
 */

export interface ScoringResult {
  runsScored: number;
  isLegalDelivery: boolean;
  shouldRotateStrike: boolean;
  isWicket: boolean;
  isExtra: boolean;
}

/**
 * Analyze a ball outcome and return scoring information
 */
export const analyzeBallOutcome = (outcome: BallOutcome): ScoringResult => {
  switch (outcome) {
    case BallOutcome.DOT:
    case BallOutcome.SINGLE: 
    case BallOutcome.DOUBLE:
    case BallOutcome.TRIPLE:
    case BallOutcome.FOUR:
    case BallOutcome.SIX:
      const runs = outcome as number;
      return {
        runsScored: runs,
        isLegalDelivery: true,
        shouldRotateStrike: runs === 1 || runs === 3,
        isWicket: false,
        isExtra: false,
      };
    
    case BallOutcome.WIDE:
      return {
        runsScored: 1,
        isLegalDelivery: false,
        shouldRotateStrike: false,
        isWicket: false,
        isExtra: true,
      };
    
    case BallOutcome.NO_BALL:
      return {
        runsScored: 1,
        isLegalDelivery: false,
        shouldRotateStrike: false,
        isWicket: false,
        isExtra: true,
      };
    
    case BallOutcome.WICKET:
      return {
        runsScored: 0,
        isLegalDelivery: true,
        shouldRotateStrike: false,
        isWicket: true,
        isExtra: false,
      };
    
    default:
      throw new Error(`Unknown ball outcome: ${outcome}`);
  }
};

/**
 * Calculate over progress from balls bowled
 */
export const calculateOverProgress = (ballsInCurrentOver: number, oversCompleted: number) => {
  return {
    oversCompleted,
    ballsInCurrentOver,
    overDisplay: `${oversCompleted}.${ballsInCurrentOver}`,
    isOverComplete: ballsInCurrentOver >= 6,
  };
};

/**
 * Update batsman stats for a given ball outcome
 */
export const updateBatsmanStats = (
  stats: PlayerBattingStats, 
  outcome: BallOutcome,
  isLegalDelivery: boolean,
  runsScored: number
): PlayerBattingStats => {
  const updatedStats = { ...stats };

  // Update runs and balls faced
  updatedStats.runsScored += runsScored;
  if (isLegalDelivery) {
    updatedStats.ballsFaced += 1;
  }

  // Update boundaries
  if (runsScored === 4) updatedStats.fours += 1;
  if (runsScored === 6) updatedStats.sixes += 1;

  // Update status for wicket
  if (outcome === BallOutcome.WICKET) {
    updatedStats.status = 'out';
  }

  return updatedStats;
};

/**
 * Update bowler stats for a given ball outcome
 */
export const updateBowlerStats = (
  stats: PlayerBowlingStats,
  outcome: BallOutcome,
  isLegalDelivery: boolean,
  runsScored: number
): PlayerBowlingStats => {
  const updatedStats = { ...stats };

  // Update balls bowled and runs conceded
  if (isLegalDelivery) {
    updatedStats.ballsBowled += 1;
  }
  updatedStats.runsConceded += runsScored;

  // Update specific extra types
  if (outcome === BallOutcome.WIDE) {
    updatedStats.widesBowled += 1;
  } else if (outcome === BallOutcome.NO_BALL) {
    updatedStats.noBallsBowled += 1;
  }

  // Update wickets
  if (outcome === BallOutcome.WICKET) {
    updatedStats.wicketsTaken += 1;
  }

  return updatedStats;
};

/**
 * Check if over is completed and should be marked as maiden
 */
export const checkMaidenOver = (runsConcededThisOver: number): boolean => {
  return runsConcededThisOver === 0;
};

/**
 * Calculate maximum wickets allowed for innings end
 */
export const getMaxWicketsForInnings = (playersPerTeam: number, lastManStanding: boolean): number => {
  return lastManStanding ? playersPerTeam : playersPerTeam - 1;
};

/**
 * Check if innings should end based on wickets or overs
 */
export const shouldInningsEnd = (
  wickets: number,
  oversCompleted: number,
  maxOvers: number,
  maxWickets: number
): boolean => {
  return wickets >= maxWickets || oversCompleted >= maxOvers;
};

/**
 * Check if target is achieved in second innings
 */
export const isTargetAchieved = (currentScore: number, target: number): boolean => {
  return currentScore >= target;
};

/**
 * Generate match result description
 */
export const generateMatchResult = (
  match: Match,
  winningTeam: string,
  isTargetAchieved: boolean,
  wicketsLost: number
): string => {
  if (isTargetAchieved) {
    const wicketsRemaining = match.playersPerTeam - wicketsLost;
    return `${winningTeam} won by ${wicketsRemaining} wickets.`;
  } else {
    // Calculate runs margin (for first innings winner)
    const firstInningsScore = match.firstInnings?.score || 0;
    const secondInningsScore = match.secondInnings?.score || 0;
    const runMargin = Math.abs(firstInningsScore - secondInningsScore);
    
    if (firstInningsScore === secondInningsScore) {
      return "Match Tied!";
    }
    
    return `${winningTeam} won by ${runMargin} runs.`;
  }
};

/**
 * Get available batsmen (not out and not currently batting)
 */
export const getAvailableBatsmen = (
  teamPlayers: Player[],
  currentStrikerId?: string | null,
  currentNonStrikerId?: string | null
): Player[] => {
  return teamPlayers.filter(player => 
    player.battingStats.status !== 'out' &&
    player.id !== currentStrikerId &&
    player.id !== currentNonStrikerId
  );
};

/**
 * Get all bowlers from bowling team
 */
export const getAvailableBowlers = (teamPlayers: Player[]): Player[] => {
  return [...teamPlayers]; // All players can bowl in gully cricket
};

/**
 * Calculate player strike rate
 */
export const calculateStrikeRate = (runsScored: number, ballsFaced: number): number => {
  if (ballsFaced === 0) return 0;
  return (runsScored / ballsFaced) * 100;
};

/**
 * Calculate bowler economy rate
 */
export const calculateEconomyRate = (runsConceded: number, ballsBowled: number): number => {
  if (ballsBowled === 0) return 0;
  const overs = ballsBowled / 6;
  return runsConceded / overs;
};

/**
 * Format over display (e.g., "19.4" for 19 overs and 4 balls)
 */
export const formatOverDisplay = (oversCompleted: number, ballsInCurrentOver: number): string => {
  return `${oversCompleted}.${ballsInCurrentOver}`;
};