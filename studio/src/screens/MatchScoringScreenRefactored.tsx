import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { ScreenNavigationProp, MatchScoringRouteProp } from '../navigation/AppNavigator';
import { Player, BallOutcome } from '../types/matchTypes';

// Custom Hooks
import { useMatchState } from '../hooks/useMatchState';
import { useCricketScoring } from '../hooks/useCricketScoring';

// Components
import ScoreDisplay from '../components/scoring/ScoreDisplay';
import ScoringControls from '../components/scoring/ScoringControls';
import PlayerSelectionCard from '../components/scoring/PlayerSelectionCard';
import DismissalTypeSelector, { DismissalType } from '../components/scoring/DismissalTypeSelector';
import OverEndPlayerSelector from '../components/scoring/OverEndPlayerSelector';

/**
 * Simplified MatchScoringScreen using extracted logic and components
 * This replaces the 2,592 line monolithic component
 */

enum UIState {
  LOADING = 'LOADING',
  SELECTING_BATSMEN = 'SELECTING_BATSMEN',
  SELECTING_BOWLER = 'SELECTING_BOWLER',
  SCORING_ACTIVE = 'SCORING_ACTIVE',
  SELECTING_DISMISSAL_TYPE = 'SELECTING_DISMISSAL_TYPE',
  SELECTING_NEW_BATSMAN = 'SELECTING_NEW_BATSMAN',
  SELECTING_NEW_BOWLER = 'SELECTING_NEW_BOWLER',
  OVER_END_PLAYER_SELECTION = 'OVER_END_PLAYER_SELECTION',
  MATCH_COMPLETE = 'MATCH_COMPLETE',
  ERROR = 'ERROR',
}

export default function MatchScoringScreenRefactored() {
  const route = useRoute<MatchScoringRouteProp>();
  const navigation = useNavigation<ScreenNavigationProp<'MatchScoring'>>();
  const { matchId } = route.params;

  // State management
  const { match, isLoading, error, updateMatch } = useMatchState(matchId);
  const cricketScoring = useCricketScoring(match, updateMatch);
  const [uiState, setUIState] = useState<UIState>(UIState.LOADING);
  const [isProcessing, setIsProcessing] = useState(false);

  // Player selection state
  const [selectedStriker, setSelectedStriker] = useState<Player | null>(null);
  const [selectedNonStriker, setSelectedNonStriker] = useState<Player | null>(null);
  const [selectedBowler, setSelectedBowler] = useState<Player | null>(null);

  // Gully cricket specific state
  const [dismissedPlayer, setDismissedPlayer] = useState<Player | null>(null);
  const [selectedDismissalType, setSelectedDismissalType] = useState<DismissalType | null>(null);
  const [showOverEndSelection, setShowOverEndSelection] = useState(false);

  // Determine UI state based on match state
  useEffect(() => {
    if (isLoading) {
      setUIState(UIState.LOADING);
      return;
    }

    if (error || !match) {
      setUIState(UIState.ERROR);
      return;
    }

    if (match.status === 'completed') {
      setUIState(UIState.MATCH_COMPLETE);
      return;
    }

    const currentInnings = cricketScoring.getCurrentInnings();
    if (!currentInnings) {
      setUIState(UIState.ERROR);
      return;
    }

    // Check what selections are needed
    const needsStriker = !currentInnings.strikerId;
    const needsNonStriker = !currentInnings.nonStrikerId && 
      (!match.lastManStanding || currentInnings.wickets < match.playersPerTeam - 1);
    const needsBowler = !currentInnings.currentBowlerId;

    if (needsStriker || needsNonStriker) {
      setUIState(UIState.SELECTING_BATSMEN);
    } else if (needsBowler) {
      setUIState(UIState.SELECTING_BOWLER);
    } else if (cricketScoring.canPlayBall) {
      setUIState(UIState.SCORING_ACTIVE);
    } else {
      setUIState(UIState.ERROR);
    }
  }, [match, isLoading, error, cricketScoring]);

  // Handle ball playing
  const handlePlayBall = async (outcome: BallOutcome) => {
    if (!cricketScoring.canPlayBall || isProcessing) return;

    setIsProcessing(true);
    try {
      const matchUpdate = await cricketScoring.playBall(outcome);
      
      if (matchUpdate) {
        // Handle alerts
        matchUpdate.alerts.forEach(alert => {
          Alert.alert('Match Update', alert);
        });

        // Handle state transitions
        if (matchUpdate.isMatchComplete) {
          setUIState(UIState.MATCH_COMPLETE);
          Alert.alert('Match Over!', matchUpdate.updatedMatch.resultDescription || 'Match completed.');
          navigation.navigate('MatchSummary', { matchId: matchUpdate.updatedMatch.id });
        } else if (matchUpdate.shouldSelectNewBatsman) {
          // In gully cricket, first select dismissal type, then new batsman
          const currentInnings = cricketScoring.getCurrentInnings();
          if (currentInnings?.strikerId) {
            const batterTeamPlayers = currentInnings.battingTeamName === match?.teamAName
              ? match?.teamAPlayers
              : match?.teamBPlayers;
            const dismissedPlayerObj = batterTeamPlayers?.find(p => p.id === currentInnings.strikerId);
            if (dismissedPlayerObj) {
              setDismissedPlayer(dismissedPlayerObj);
              setUIState(UIState.SELECTING_DISMISSAL_TYPE);
            } else {
              // Fallback to direct batsman selection
              setSelectedStriker(null);
              setSelectedNonStriker(null);
              setUIState(UIState.SELECTING_NEW_BATSMAN);
            }
          }
        } else if (matchUpdate.shouldSelectNewBowler) {
          // Check if we should show over-end player selection first
          setShowOverEndSelection(true);
          setUIState(UIState.OVER_END_PLAYER_SELECTION);
        } else if (matchUpdate.isInningsComplete && match?.status === 'live_inning2') {
          // New innings started, need to select batsmen
          setSelectedStriker(null);
          setSelectedNonStriker(null);
          setUIState(UIState.SELECTING_BATSMEN);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to record ball. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle batsmen confirmation
  const handleConfirmBatsmen = async () => {
    if (!selectedStriker) {
      Alert.alert('Selection Required', 'Please select a striker.');
      return;
    }

    // Check if we're in last man standing scenario where only striker is needed
    // This should only apply when there's literally only 1 player left (not out)
    const currentInnings = cricketScoring.getCurrentInnings();
    const availableBatsmen = cricketScoring.getAvailableBatsmenForSelection();
    const isLastManStandingScenario = match?.lastManStanding &&
                                     currentInnings &&
                                     availableBatsmen.length <= 1;

    if (!isLastManStandingScenario && !selectedNonStriker) {
      Alert.alert('Selection Required', 'Please select both striker and non-striker.');
      return;
    }

    setIsProcessing(true);
    try {
      // This would need to be implemented in the cricket scoring hook
      // For now, we'll update the match directly
      if (match) {
        const currentInnings = cricketScoring.getCurrentInnings();
        if (currentInnings) {
          const updatedMatch = { ...match };
          const inningsToUpdate = updatedMatch.status === 'live_inning1' 
            ? updatedMatch.firstInnings 
            : updatedMatch.secondInnings;
          
          if (inningsToUpdate) {
            inningsToUpdate.strikerId = selectedStriker.id;
            inningsToUpdate.nonStrikerId = selectedNonStriker?.id ?? null;
            await updateMatch(updatedMatch);
          }
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to set batsmen. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle bowler confirmation
  const handleConfirmBowler = async () => {
    if (!selectedBowler) {
      Alert.alert('Selection Required', 'Please select a bowler.');
      return;
    }

    setIsProcessing(true);
    try {
      await cricketScoring.setNewBowler(selectedBowler);
      setSelectedBowler(null);
    } catch (error) {
      Alert.alert('Error', 'Failed to set bowler. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle new batsman selection (after wicket)
  const handleSelectNewBatsman = async (player: Player) => {
    setIsProcessing(true);
    try {
      await cricketScoring.setNewBatsman(player);
      setSelectedStriker(null);
    } catch (error) {
      Alert.alert('Error', 'Failed to set new batsman. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle dismissal type selection
  const handleDismissalTypeSelection = (dismissalType: DismissalType) => {
    setSelectedDismissalType(dismissalType);

    // Update the dismissed player's stats with dismissal type
    if (dismissedPlayer && match) {
      const updatedMatch = { ...match };
      const batterTeamPlayers = dismissedPlayer.id.includes('teamA')
        ? updatedMatch.teamAPlayers
        : updatedMatch.teamBPlayers;

      const playerToUpdate = batterTeamPlayers.find(p => p.id === dismissedPlayer.id);
      if (playerToUpdate) {
        playerToUpdate.battingStats.status = dismissalType as any; // Type assertion for now
        playerToUpdate.battingStats.dismissalFielder = undefined; // Could be enhanced later
      }
    }

    // Move to batsman selection
    setSelectedStriker(null);
    setSelectedNonStriker(null);
    setUIState(UIState.SELECTING_NEW_BATSMAN);
  };

  // Handle over-end player selection
  const handleOverEndPlayerSelection = (striker: Player | null, nonStriker: Player | null) => {
    if (striker || nonStriker) {
      // User wants to change players
      setSelectedStriker(striker);
      setSelectedNonStriker(nonStriker);
      setUIState(UIState.SELECTING_BATSMEN);
    } else {
      // User wants to keep current players, proceed to bowler selection
      setSelectedBowler(null);
      setUIState(UIState.SELECTING_NEW_BOWLER);
    }
    setShowOverEndSelection(false);
  };

  // Handle skipping over-end player selection
  const handleSkipOverEndSelection = () => {
    setShowOverEndSelection(false);
    setSelectedBowler(null);
    setUIState(UIState.SELECTING_NEW_BOWLER);
  };

  // Render loading state
  if (uiState === UIState.LOADING) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading match...</Text>
      </View>
    );
  }

  // Render error state
  if (uiState === UIState.ERROR) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          {error || 'Unable to load match data'}
        </Text>
      </View>
    );
  }

  // Render match complete state
  if (uiState === UIState.MATCH_COMPLETE) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.completedText}>Match Completed!</Text>
        <Text style={styles.resultText}>{match?.resultDescription}</Text>
      </View>
    );
  }

  if (!match) return null;

  return (
    <ScrollView style={styles.container}>
      {/* Always show score display */}
      <ScoreDisplay match={match} />

      {/* Batsmen Selection */}
      {(uiState === UIState.SELECTING_BATSMEN || uiState === UIState.SELECTING_NEW_BATSMAN) && (
        <PlayerSelectionCard
          title={uiState === UIState.SELECTING_BATSMEN ? "Select Opening Batsmen" : "Select New Batsman"}
          subtitle={`Team: ${cricketScoring.getCurrentInnings()?.battingTeamName}`}
          players={cricketScoring.getAvailableBatsmenForSelection()}
          selectedStriker={selectedStriker}
          selectedNonStriker={selectedNonStriker}
          onSelectStriker={setSelectedStriker}
          onSelectNonStriker={setSelectedNonStriker}
          onConfirm={uiState === UIState.SELECTING_BATSMEN ? handleConfirmBatsmen : () => selectedStriker && handleSelectNewBatsman(selectedStriker)}
          confirmButtonText={uiState === UIState.SELECTING_BATSMEN ? "Confirm Batsmen" : "Confirm New Batsman"}
          showBatsmenSelection={uiState === UIState.SELECTING_BATSMEN}
          canConfirm={
            uiState === UIState.SELECTING_BATSMEN
              ? !!(selectedStriker && (selectedNonStriker || (match?.lastManStanding && cricketScoring.getAvailableBatsmenForSelection().length <= 1)))
              : !!selectedStriker
          }
          loading={isProcessing}
          match={match}
          currentInnings={cricketScoring.getCurrentInnings()}
        />
      )}

      {/* Bowler Selection */}
      {(uiState === UIState.SELECTING_BOWLER || uiState === UIState.SELECTING_NEW_BOWLER) && (
        <PlayerSelectionCard
          title={uiState === UIState.SELECTING_BOWLER ? "Select Opening Bowler" : "Select Bowler for Next Over"}
          subtitle={`Team: ${cricketScoring.getCurrentInnings()?.bowlingTeamName}`}
          players={cricketScoring.getAvailableBowlersForSelection()}
          selectedBowler={selectedBowler}
          onSelectBowler={setSelectedBowler}
          onConfirm={handleConfirmBowler}
          confirmButtonText="Confirm Bowler"
          showBowlerSelection={true}
          canConfirm={!!selectedBowler}
          loading={isProcessing}
        />
      )}

      {/* Dismissal Type Selection */}
      {uiState === UIState.SELECTING_DISMISSAL_TYPE && dismissedPlayer && (
        <DismissalTypeSelector
          title="How was the batsman dismissed?"
          subtitle={`Team: ${cricketScoring.getCurrentInnings()?.battingTeamName}`}
          dismissedPlayer={dismissedPlayer}
          onSelectDismissalType={handleDismissalTypeSelection}
        />
      )}

      {/* Over End Player Selection */}
      {uiState === UIState.OVER_END_PLAYER_SELECTION && (
        <OverEndPlayerSelector
          title="Over Complete - Change Players?"
          subtitle={`Team: ${cricketScoring.getCurrentInnings()?.battingTeamName}`}
          availablePlayers={cricketScoring.getAvailableBatsmenForSelection()}
          currentStriker={cricketScoring.getCurrentInnings()?.strikerId ?
            cricketScoring.getAvailableBatsmenForSelection().find(p => p.id === cricketScoring.getCurrentInnings()?.strikerId) || null : null}
          currentNonStriker={cricketScoring.getCurrentInnings()?.nonStrikerId ?
            cricketScoring.getAvailableBatsmenForSelection().find(p => p.id === cricketScoring.getCurrentInnings()?.nonStrikerId) || null : null}
          selectedStriker={selectedStriker}
          selectedNonStriker={selectedNonStriker}
          onSelectStriker={setSelectedStriker}
          onSelectNonStriker={setSelectedNonStriker}
          onConfirm={() => handleOverEndPlayerSelection(selectedStriker, selectedNonStriker)}
          onSkip={handleSkipOverEndSelection}
          canConfirm={!!(selectedStriker || selectedNonStriker)}
          loading={isProcessing}
        />
      )}

      {/* Scoring Controls */}
      {uiState === UIState.SCORING_ACTIVE && (
        <ScoringControls
          onPlayBall={handlePlayBall}
          disabled={!cricketScoring.canPlayBall}
          loading={isProcessing}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#6b7280',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
  },
  completedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#059669',
    marginBottom: 16,
  },
  resultText: {
    fontSize: 18,
    color: '#374151',
    textAlign: 'center',
  },
});