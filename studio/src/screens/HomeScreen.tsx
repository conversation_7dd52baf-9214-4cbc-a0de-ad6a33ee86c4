import React from 'react';
import { View, Text, StyleSheet, Alert, ScrollView, TouchableHighlight, Platform, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScreenNavigationProp } from '../navigation/AppNavigator';

const ICON_COLOR = "#4f46e5";

type HomeScreenNavigationProp = ScreenNavigationProp<'Home'>;

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // Debug logging
  console.log('🏠 HomeScreen loaded successfully');
  console.log('📱 Device info:', {
    platform: 'iOS',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'
  });

  const handleStartMatch = () => {
    console.log('Start Match pressed');
    Alert.alert('Button Pressed!', 'Start Match button was pressed successfully!');
    try {
      navigation.navigate('ConfigureMatch');
    } catch (error) {
      console.error('Navigation failed:', error);
      Alert.alert('Error', `Navigation failed: ${error}`);
    }
  };

  const handleViewHistory = () => {
    console.log('View History pressed');
    Alert.alert('Button Pressed!', 'View History button was pressed successfully!');
    try {
      navigation.navigate('MatchHistory');
    } catch (error) {
      console.error('Navigation failed:', error);
      Alert.alert('Error', `Navigation failed: ${error}`);
    }
  };

  const handleTouchTest = () => {
    console.log('Touch Test pressed');
    Alert.alert('Button Pressed!', 'Touch Test button was pressed successfully!');
    try {
      navigation.navigate('TouchTest');
    } catch (error) {
      console.error('Navigation failed:', error);
      Alert.alert('Error', `Navigation failed: ${error}`);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.headerCard}>
        <Image
          source={{ uri: 'https://picsum.photos/seed/cricketpitch/120/120' }}
          style={styles.logoImage}
        />
        <Text style={styles.title}>Welcome to GullyScore!</Text>
        <Text style={styles.description}>
          Your companion for scoring gully cricket matches with ease and fun.
        </Text>

        {/* Touch Detection Test */}
        <View
          style={styles.touchTestArea}
          onTouchStart={() => Alert.alert('Touch Detected!', 'onTouchStart works on iPhone 13')}
        >
          <Text style={styles.touchTestText}>
            👆 TAP THIS AREA TO TEST TOUCH DETECTION
          </Text>
        </View>
        <View style={styles.buttonGroup}>
          <TouchableHighlight
            style={styles.primaryButton}
            onPress={handleStartMatch}
            underlayColor="#3730a3"
            activeOpacity={0.8}
          >
            <Text style={styles.primaryButtonText}>Start New Match</Text>
          </TouchableHighlight>

          <TouchableHighlight
            style={styles.secondaryButton}
            onPress={handleViewHistory}
            underlayColor="#e5e7eb"
            activeOpacity={0.8}
          >
            <Text style={styles.secondaryButtonText}>View Match History</Text>
          </TouchableHighlight>

          <TouchableHighlight
            style={styles.testButton}
            onPress={handleTouchTest}
            underlayColor="#b91c1c"
            activeOpacity={0.8}
          >
            <Text style={styles.testButtonText}>🔧 Touch Test (iPhone 13)</Text>
          </TouchableHighlight>
        </View>
      </View>

      <View style={styles.featuresSection}>
        <Text style={styles.featuresTitle}>Why GullyScore?</Text>
        <View style={styles.featuresGrid}>
          <View style={styles.featureCard}>
            <Text style={styles.featureCardTitle}>Quick Setup</Text>
            <Text style={styles.featureCardText}>Start matches in seconds with team names and overs.</Text>
          </View>
          <View style={styles.featureCard}>
            <Text style={styles.featureCardTitle}>Real-time Scoring</Text>
            <Text style={styles.featureCardText}>Input scores, wickets, and extras as they happen.</Text>
          </View>
          <View style={styles.featureCard}>
            <Text style={styles.featureCardTitle}>Match History</Text>
            <Text style={styles.featureCardText}>Save and revisit your completed matches anytime.</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
    paddingBottom: 40,
    paddingHorizontal: 16,
  },
  headerCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5, // for Android shadow
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: ICON_COLOR,
    marginBottom: 16,
  },
  title: {
    fontSize: 26, // Adjusted size for mobile
    fontWeight: 'bold',
    color: '#1f2937', // Darker gray, like text-gray-800
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#4b5563', // Medium gray, like text-gray-600
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonGroup: {
    width: '100%',
    marginTop: 20,
  },
  featuresSection: {
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  featuresTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  featuresGrid: {
    width: '100%',
    // For a 1-column layout on mobile, items will stack. 
    // For a grid, you might use flexWrap: 'wrap' and assign widths to featureCard if you don't want to use FlatList with numColumns
  },
  featureCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16, // Slightly smaller padding for feature cards
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2.22,
    elevation: 3,
  },
  featureIcon: {
    marginBottom: 12,
  },
  featureCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  featureCardText: {
    fontSize: 14,
    color: '#4b5563',
    textAlign: 'center',
  },
  primaryButton: {
    backgroundColor: '#4f46e5',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#6b7280',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  secondaryButtonText: {
    color: '#6b7280',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  testButton: {
    backgroundColor: '#dc2626',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  testButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  touchTestArea: {
    backgroundColor: '#fef2f2',
    borderWidth: 2,
    borderColor: '#dc2626',
    borderStyle: 'dashed',
    padding: 20,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  touchTestText: {
    color: '#dc2626',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
