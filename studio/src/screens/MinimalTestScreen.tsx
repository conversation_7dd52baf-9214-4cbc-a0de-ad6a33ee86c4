import React from 'react';
import { View, Text, Alert } from 'react-native';

const MinimalTestScreen: React.FC = () => {
  const handleTouch = () => {
    Alert.alert('SUCCESS!', 'Touch is working on iPhone 13!');
  };

  return (
    <View style={{
      flex: 1,
      backgroundColor: 'red',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    }}>
      <Text style={{
        fontSize: 24,
        color: 'white',
        textAlign: 'center',
        marginBottom: 40,
        fontWeight: 'bold',
      }}>
        MINIMAL TOUCH TEST
      </Text>
      
      <View
        style={{
          backgroundColor: 'white',
          padding: 30,
          borderRadius: 10,
          width: '100%',
          alignItems: 'center',
        }}
        onTouchEnd={handleTouch}
      >
        <Text style={{
          fontSize: 20,
          color: 'black',
          fontWeight: 'bold',
          textAlign: 'center',
        }}>
          TAP THIS WHITE BOX
        </Text>
      </View>
      
      <Text style={{
        fontSize: 16,
        color: 'white',
        textAlign: 'center',
        marginTop: 40,
      }}>
        If you see an alert, touch works!
        If not, it's an Expo Go + iPhone 13 issue.
      </Text>
    </View>
  );
};

export default MinimalTestScreen;
