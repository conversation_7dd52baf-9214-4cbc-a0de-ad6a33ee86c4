import React from 'react';
import { View, Text, StyleSheet, Alert, Button, TouchableOpacity, Pressable } from 'react-native';

const TouchTestScreen: React.FC = () => {
  const showAlert = (buttonType: string) => {
    Alert.alert('Touch Works!', `${buttonType} button was pressed successfully on iPhone 13!`);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>iPhone 13 Touch Test</Text>
      <Text style={styles.subtitle}>Try all buttons below:</Text>

      {/* Native Button */}
      <View style={styles.buttonContainer}>
        <Text style={styles.label}>1. Native Button:</Text>
        <Button
          title="Native Button Test"
          onPress={() => showAlert('Native')}
          color="#4f46e5"
        />
      </View>

      {/* TouchableOpacity */}
      <View style={styles.buttonContainer}>
        <Text style={styles.label}>2. TouchableOpacity:</Text>
        <TouchableOpacity
          style={styles.customButton}
          onPress={() => showAlert('TouchableOpacity')}
          activeOpacity={0.7}
        >
          <Text style={styles.buttonText}>TouchableOpacity Test</Text>
        </TouchableOpacity>
      </View>

      {/* Pressable */}
      <View style={styles.buttonContainer}>
        <Text style={styles.label}>3. Pressable:</Text>
        <Pressable
          style={({ pressed }) => [
            styles.customButton,
            pressed && styles.pressed
          ]}
          onPress={() => showAlert('Pressable')}
        >
          <Text style={styles.buttonText}>Pressable Test</Text>
        </Pressable>
      </View>

      {/* Simple View with onTouchEnd */}
      <View style={styles.buttonContainer}>
        <Text style={styles.label}>4. View with onTouchEnd:</Text>
        <View
          style={styles.customButton}
          onTouchEnd={() => showAlert('View onTouchEnd')}
        >
          <Text style={styles.buttonText}>View onTouchEnd Test</Text>
        </View>
      </View>

      {/* View with Responder */}
      <View style={styles.buttonContainer}>
        <Text style={styles.label}>5. View with Responder:</Text>
        <View
          style={styles.customButton}
          onStartShouldSetResponder={() => true}
          onResponderGrant={() => showAlert('View Responder')}
        >
          <Text style={styles.buttonText}>View Responder Test</Text>
        </View>
      </View>

      <Text style={styles.instructions}>
        If ANY button shows an alert, touch is working!
        Report which buttons work and which don't.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f3f4f6',
    paddingTop: 60,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#6b7280',
  },
  buttonContainer: {
    marginBottom: 25,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  customButton: {
    backgroundColor: '#4f46e5',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    minHeight: 50,
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  pressed: {
    opacity: 0.7,
    transform: [{ scale: 0.98 }],
  },
  instructions: {
    fontSize: 12,
    textAlign: 'center',
    color: '#6b7280',
    fontStyle: 'italic',
    marginTop: 20,
    lineHeight: 18,
  },
});

export default TouchTestScreen;
