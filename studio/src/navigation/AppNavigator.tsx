import React from 'react';
import { NavigationContainer, RouteProp } from '@react-navigation/native';
import { createNativeStackNavigator, NativeStackNavigationProp } from '@react-navigation/native-stack';

import MinimalTestScreen from '../screens/MinimalTestScreen';
import ConfigureMatchScreen from '../screens/ConfigureMatchScreen';
import TossScreen from '../screens/TossScreen';
import MatchHistoryScreen from '../screens/MatchHistoryScreen';
import MatchScoringScreenRefactored from '../screens/MatchScoringScreenRefactored';
import MatchSummaryScreen from '../screens/MatchSummaryScreen';
import TouchTestScreen from '../screens/TouchTestScreen';

// Define the types for parameters for each screen
export type ConfigureMatchParams = {
  // Currently undefined, but if ConfigureMatchScreen itself needed params, they'd go here
};

export type TossScreenParams = {
  matchId: string;
};

export type MatchScoringParams = {
  matchId: string;
};

export type MatchSummaryParams = {
  matchId: string;
};

export type RootStackParamList = {
  Home: undefined;
  ConfigureMatch: ConfigureMatchParams | undefined; // Or just undefined if no params
  Toss: TossScreenParams;
  MatchHistory: undefined;
  MatchScoring: MatchScoringParams;
  MatchSummary: MatchSummaryParams;
  TouchTest: undefined;
};

export type ScreenNavigationProp<T extends keyof RootStackParamList> = NativeStackNavigationProp<RootStackParamList, T>;

// Types for Route Props if you need to access route.params strongly typed
export type TossScreenRouteProp = RouteProp<RootStackParamList, 'Toss'>;
export type MatchScoringRouteProp = RouteProp<RootStackParamList, 'MatchScoring'>;
export type MatchSummaryRouteProp = RouteProp<RootStackParamList, 'MatchSummary'>;

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen name="Home" component={MinimalTestScreen} options={{ title: 'Touch Test' }} />
        <Stack.Screen name="ConfigureMatch" component={ConfigureMatchScreen} options={{ title: 'Configure Match' }} />
        <Stack.Screen name="Toss" component={TossScreen} options={{ title: 'Coin Toss' }} />
        <Stack.Screen name="MatchHistory" component={MatchHistoryScreen} options={{ title: 'Match History' }} />
        <Stack.Screen name="MatchScoring" component={MatchScoringScreenRefactored} options={{ title: 'Live Score' }} />
        <Stack.Screen name="MatchSummary" component={MatchSummaryScreen} options={{ title: 'Match Summary' }} />
        <Stack.Screen name="TouchTest" component={TouchTestScreen} options={{ title: 'Touch Test' }} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
