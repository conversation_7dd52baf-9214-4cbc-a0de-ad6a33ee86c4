import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Match, Player } from '../../types/matchTypes';
import { formatOverDisplay, calculateStrikeRate } from '../../services/cricketScoring';

interface ScoreDisplayProps {
  match: Match;
}

/**
 * Score display component showing current match state
 * Displays score, overs, current players, and key stats
 */
export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({ match }) => {
  const currentInnings = match.status === 'live_inning1' 
    ? match.firstInnings 
    : match.secondInnings;

  if (!currentInnings) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No active innings</Text>
      </View>
    );
  }

  // Get current players
  const battingTeamPlayers = currentInnings.battingTeamName === match.teamAName 
    ? match.teamAPlayers 
    : match.teamBPlayers;
  
  const bowlingTeamPlayers = currentInnings.bowlingTeamName === match.teamAName 
    ? match.teamAPlayers 
    : match.teamBPlayers;

  const striker = battingTeamPlayers.find(p => p.id === currentInnings.strikerId);
  const nonStriker = battingTeamPlayers.find(p => p.id === currentInnings.nonStrikerId);
  const bowler = bowlingTeamPlayers.find(p => p.id === currentInnings.currentBowlerId);

  const formatPlayerStats = (player: Player, isStriker: boolean = false) => {
    const strikeRate = calculateStrikeRate(
      player.battingStats.runsScored, 
      player.battingStats.ballsFaced
    );
    
    return `${player.name}${isStriker ? '*' : ''} (${player.battingStats.runsScored}/${player.battingStats.ballsFaced}${player.battingStats.ballsFaced > 0 ? `, SR: ${strikeRate.toFixed(1)}` : ''})`;
  };

  const formatBowlerStats = (bowler: Player) => {
    const overs = Math.floor(bowler.bowlingStats.ballsBowled / 6);
    const balls = bowler.bowlingStats.ballsBowled % 6;
    return `${bowler.name} (${bowler.bowlingStats.wicketsTaken}/${bowler.bowlingStats.runsConceded} O: ${overs}.${balls})`;
  };

  return (
    <View style={styles.container}>
      {/* Match Header */}
      <View style={styles.header}>
        <Text style={styles.headerText}>
          {match.teamAName} vs {match.teamBName}
        </Text>
        <Text style={styles.subHeaderText}>
          {match.oversPerInnings} Over Match ({match.playersPerTeam} players/team)
          {match.secondInnings?.target ? ` | Target: ${match.secondInnings.target}` : ''}
        </Text>
        {match.tossWinnerTeamName && match.decision && (
          <Text style={styles.tossInfo}>
            {match.tossWinnerTeamName} won toss & chose to {match.decision}.
          </Text>
        )}
        {match.lastManStanding && (
          <Text style={styles.lmsInfo}>Last Man Standing Enabled</Text>
        )}
      </View>

      {/* Current Score */}
      <View style={styles.scoreContainer}>
        <View style={styles.scoreRow}>
          <Text style={styles.scoreText} testID="total-score">
            {currentInnings.score}/{currentInnings.wickets}
          </Text>
          <Text style={styles.oversText}>
            {formatOverDisplay(currentInnings.oversCompleted, currentInnings.ballsInCurrentOver)}
          </Text>
        </View>
        <Text style={styles.inningsLabel}>
          {match.status === 'live_inning1' ? '1st' : '2nd'} Innings - {currentInnings.battingTeamName} Batting
        </Text>
      </View>

      {/* Current Players */}
      <View style={styles.playersContainer}>
        <View style={styles.playersSection}>
          <Text style={styles.sectionTitle}>Current Batsmen</Text>
          {striker && (
            <Text style={styles.playerText} testID="striker-info">
              Striker: {formatPlayerStats(striker, true)}
            </Text>
          )}
          {nonStriker && (
            <Text style={styles.playerText} testID="non-striker-info">
              Non-Striker: {formatPlayerStats(nonStriker)}
            </Text>
          )}
          {match.lastManStanding && !nonStriker && striker && (
            <Text style={styles.lmsText}>Last Man Standing</Text>
          )}
        </View>

        <View style={styles.playersSection}>
          <Text style={styles.sectionTitle}>Current Bowler</Text>
          {bowler && (
            <Text style={styles.playerText} testID="bowler-info">
              {formatBowlerStats(bowler)}
            </Text>
          )}
        </View>
      </View>

      {/* Current Over */}
      <View style={styles.currentOverContainer}>
        <Text style={styles.currentOverTitle}>This Over:</Text>
        <Text style={styles.currentOverText}>
          {currentInnings.currentOverHistory.length > 0 
            ? currentInnings.currentOverHistory.join(', ')
            : 'No balls bowled yet'
          }
        </Text>
      </View>

      {/* Required Run Rate (for 2nd innings) */}
      {match.status === 'live_inning2' && match.secondInnings?.target && (
        <View style={styles.rrContainer}>
          <Text style={styles.rrText}>
            Need {match.secondInnings.target - currentInnings.score} runs 
            from {(match.oversPerInnings - currentInnings.oversCompleted) * 6 - currentInnings.ballsInCurrentOver} balls
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
  },
  subHeaderText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 4,
  },
  tossInfo: {
    fontSize: 12,
    color: '#4f46e5',
    marginTop: 4,
    fontWeight: '500',
  },
  lmsInfo: {
    fontSize: 12,
    color: '#059669',
    marginTop: 2,
    fontStyle: 'italic',
  },
  scoreContainer: {
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
  },
  scoreRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 16,
  },
  scoreText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  oversText: {
    fontSize: 20,
    color: '#6b7280',
    fontWeight: '600',
  },
  inningsLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  playersContainer: {
    marginBottom: 16,
  },
  playersSection: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  playerText: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 2,
  },
  lmsText: {
    fontSize: 12,
    color: '#059669',
    fontStyle: 'italic',
    marginTop: 2,
  },
  currentOverContainer: {
    marginBottom: 12,
    padding: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 6,
  },
  currentOverTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  currentOverText: {
    fontSize: 14,
    color: '#6b7280',
  },
  rrContainer: {
    padding: 8,
    backgroundColor: '#fef3c7',
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
  },
  rrText: {
    fontSize: 14,
    color: '#92400e',
    fontWeight: '500',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
  },
});

export default ScoreDisplay;