import React from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { BallOutcome } from '../../types/matchTypes';
import Button from '../Button';

interface ScoringControlsProps {
  onPlayBall: (outcome: BallOutcome) => Promise<void>;
  disabled?: boolean;
  loading?: boolean;
}

/**
 * Cricket scoring controls component
 * Provides buttons for all possible ball outcomes
 */
export const ScoringControls: React.FC<ScoringControlsProps> = ({
  onPlayBall,
  disabled = false,
  loading = false,
}) => {

  const handleBallOutcome = async (outcome: BallOutcome) => {
    if (disabled || loading) return;
    
    try {
      await onPlayBall(outcome);
    } catch (error) {
      Alert.alert('Error', 'Failed to record ball outcome. Please try again.');
    }
  };

  const renderRunButtons = () => {
    const runs = [BallOutcome.DOT, BallOutcome.SINGLE, BallOutcome.DOUBLE, BallOutcome.TRIPLE];
    
    return (
      <View style={styles.buttonRow}>
        {runs.map((runsValue) => (
          <Button
            key={String(runsValue)}
            title={String(runsValue)}
            onPress={() => handleBallOutcome(runsValue)}
            variant="outline"
            size="large"
            disabled={disabled || loading}
            loading={loading}
            style={styles.scoringButton}
          />
        ))}
      </View>
    );
  };

  const renderBoundaryButtons = () => {
    const boundaries = [BallOutcome.FOUR, BallOutcome.SIX];
    
    return (
      <View style={styles.buttonRow}>
        {boundaries.map((boundary) => (
          <Button
            key={String(boundary)}
            title={String(boundary)}
            onPress={() => handleBallOutcome(boundary)}
            variant="primary"
            size="large"
            disabled={disabled || loading}
            loading={loading}
            style={styles.boundaryButton}
          />
        ))}
      </View>
    );
  };

  const renderExtraButtons = () => {
    const extras = [BallOutcome.WIDE, BallOutcome.NO_BALL];
    
    return (
      <View style={styles.buttonRow}>
        {extras.map((extra) => (
          <Button
            key={String(extra)}
            title={extra === BallOutcome.NO_BALL ? 'No Ball' : String(extra)}
            onPress={() => handleBallOutcome(extra)}
            variant="outline"
            size="large"
            disabled={disabled || loading}
            loading={loading}
            style={styles.extraButton}
          />
        ))}
      </View>
    );
  };

  const renderWicketButton = () => (
    <View style={styles.wicketRow}>
      <Button
        title="Wicket"
        onPress={() => handleBallOutcome(BallOutcome.WICKET)}
        variant="destructive"
        size="large"
        disabled={disabled || loading}
        loading={loading}
        style={styles.wicketButton}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Record Ball:</Text>
      
      {/* Runs: 0, 1, 2, 3 */}
      {renderRunButtons()}
      
      {/* Boundaries: 4, 6 */}
      {renderBoundaryButtons()}
      
      {/* Extras: Wide, No Ball */}
      {renderExtraButtons()}
      
      {/* Wicket */}
      {renderWicketButton()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#1f2937',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
    gap: 8,
  },
  wicketRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  scoringButton: {
    flex: 1,
    marginHorizontal: 2,
  },
  boundaryButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  extraButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  wicketButton: {
    minWidth: 120,
    paddingHorizontal: 24,
  },
});

export default ScoringControls;