import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Player } from '../../types/matchTypes';
import Button from '../Button';

export enum DismissalType {
  BOWLED = 'bowled',
  CAUGHT = 'caught',
  LBW = 'lbw',
  RUN_OUT = 'run_out',
  STUMPED = 'stumped',
  HIT_WICKET = 'hit_wicket',
  HANDLED_BALL = 'handled_ball',
  OBSTRUCTING_FIELD = 'obstructing_field',
  TIMED_OUT = 'timed_out',
}

interface DismissalTypeSelectorProps {
  title: string;
  subtitle?: string;
  dismissedPlayer: Player;
  onSelectDismissalType: (type: DismissalType) => void;
  onCancel?: () => void;
}

export const DismissalTypeSelector: React.FC<DismissalTypeSelectorProps> = ({
  title,
  subtitle,
  dismissedPlayer,
  onSelectDismissalType,
  onCancel,
}) => {
  const dismissalTypes = [
    { type: DismissalType.BOWLED, label: 'Bowled', description: 'Ball hits the stumps directly' },
    { type: DismissalType.CAUGHT, label: 'Caught', description: 'Ball caught by fielder' },
    { type: DismissalType.LBW, label: 'LBW', description: 'Leg Before Wicket' },
    { type: DismissalType.RUN_OUT, label: 'Run Out', description: 'Stumps broken while running' },
    { type: DismissalType.STUMPED, label: 'Stumped', description: 'Wicket-keeper breaks stumps' },
    { type: DismissalType.HIT_WICKET, label: 'Hit Wicket', description: 'Batsman hits own stumps' },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        <Text style={styles.playerInfo}>
          Dismissed Player: {dismissedPlayer.name}
        </Text>
      </View>

      <View style={styles.dismissalGrid}>
        {dismissalTypes.map((dismissal) => (
          <View key={dismissal.type} style={styles.dismissalOption}>
            <Button
              title={dismissal.label}
              onPress={() => onSelectDismissalType(dismissal.type)}
              variant="outline"
              size="medium"
              style={styles.dismissalButton}
            />
            <Text style={styles.dismissalDescription}>{dismissal.description}</Text>
          </View>
        ))}
      </View>

      {onCancel && (
        <View style={styles.cancelContainer}>
          <Button
            title="Cancel"
            onPress={onCancel}
            variant="ghost"
            size="medium"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
    textAlign: 'center',
  },
  playerInfo: {
    fontSize: 16,
    fontWeight: '600',
    color: '#dc2626',
    textAlign: 'center',
    backgroundColor: '#fef2f2',
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  dismissalGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  dismissalOption: {
    width: '48%',
    marginBottom: 16,
  },
  dismissalButton: {
    marginBottom: 4,
  },
  dismissalDescription: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 16,
  },
  cancelContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
});

export default DismissalTypeSelector;
