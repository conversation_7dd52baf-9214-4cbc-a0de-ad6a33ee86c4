import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Player } from '../../types/matchTypes';
import PlayerDropdown from '../PlayerDropdown';
import Button from '../Button';

interface OverEndPlayerSelectorProps {
  title: string;
  subtitle?: string;
  availablePlayers: Player[];
  currentStriker: Player | null;
  currentNonStriker: Player | null;
  selectedStriker: Player | null;
  selectedNonStriker: Player | null;
  onSelectStriker: (player: Player) => void;
  onSelectNonStriker: (player: Player) => void;
  onConfirm: () => void;
  onSkip: () => void;
  canConfirm: boolean;
  loading?: boolean;
}

export const OverEndPlayerSelector: React.FC<OverEndPlayerSelectorProps> = ({
  title,
  subtitle,
  availablePlayers,
  currentStriker,
  currentNonStriker,
  selectedStriker,
  selectedNonStriker,
  onSelectStriker,
  onSelectNonStriker,
  onConfirm,
  onSkip,
  canConfirm,
  loading = false,
}) => {
  const getExcludedPlayerIds = (role: 'striker' | 'nonStriker'): string[] => {
    const excluded: string[] = [];
    
    if (role === 'striker' && selectedNonStriker) {
      excluded.push(selectedNonStriker.id);
    } else if (role === 'nonStriker' && selectedStriker) {
      excluded.push(selectedStriker.id);
    }
    
    return excluded;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        
        <View style={styles.currentPlayersInfo}>
          <Text style={styles.infoLabel}>Current Players:</Text>
          <Text style={styles.playerText}>
            Striker: {currentStriker?.name || 'None'}
          </Text>
          <Text style={styles.playerText}>
            Non-Striker: {currentNonStriker?.name || 'None'}
          </Text>
        </View>
      </View>

      <View style={styles.selectionContainer}>
        <Text style={styles.sectionTitle}>Select New Players (Optional)</Text>
        
        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>New Striker:</Text>
          <PlayerDropdown
            players={availablePlayers}
            selectedPlayer={selectedStriker}
            onSelectPlayer={onSelectStriker}
            placeholder="Keep current or select new striker"
            testID="new-striker-dropdown"
            accessibilityLabel="Select new striker"
            excludePlayerIds={getExcludedPlayerIds('striker')}
          />
        </View>

        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>New Non-Striker:</Text>
          <PlayerDropdown
            players={availablePlayers}
            selectedPlayer={selectedNonStriker}
            onSelectPlayer={onSelectNonStriker}
            placeholder="Keep current or select new non-striker"
            testID="new-non-striker-dropdown"
            accessibilityLabel="Select new non-striker"
            excludePlayerIds={getExcludedPlayerIds('nonStriker')}
          />
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title="Keep Current Players"
          onPress={onSkip}
          variant="outline"
          size="medium"
          style={styles.skipButton}
          disabled={loading}
        />
        
        <Button
          title="Change Players"
          onPress={onConfirm}
          variant="primary"
          size="medium"
          disabled={!canConfirm || loading}
          loading={loading}
        />
      </View>

      <Text style={styles.helpText}>
        You can change one or both players, or keep the current lineup
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 16,
    textAlign: 'center',
  },
  currentPlayersInfo: {
    backgroundColor: '#f3f4f6',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  playerText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  selectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
    textAlign: 'center',
  },
  dropdownContainer: {
    marginBottom: 16,
  },
  dropdownLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 12,
  },
  skipButton: {
    flex: 1,
  },
  helpText: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default OverEndPlayerSelector;
