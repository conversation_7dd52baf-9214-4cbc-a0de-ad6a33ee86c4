import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Player, Match } from '../../types/matchTypes';
import PlayerDropdown from '../PlayerDropdown';
import But<PERSON> from '../Button';

interface PlayerSelectionCardProps {
  title: string;
  subtitle?: string;
  players: Player[];
  selectedStriker?: Player | null;
  selectedNonStriker?: Player | null;
  selectedBowler?: Player | null;
  onSelectStriker?: (player: Player) => void;
  onSelectNonStriker?: (player: Player) => void;
  onSelectBowler?: (player: Player) => void;
  onConfirm: () => void;
  confirmButtonText: string;
  showBowlerSelection?: boolean;
  showBatsmenSelection?: boolean;
  canConfirm: boolean;
  loading?: boolean;
  match?: Match | null;
  currentInnings?: any;
}

/**
 * Player selection card component
 * Handles batsmen and bowler selection with dropdowns
 */
export const PlayerSelectionCard: React.FC<PlayerSelectionCardProps> = ({
  title,
  subtitle,
  players,
  selectedStriker,
  selected<PERSON><PERSON><PERSON><PERSON><PERSON>,
  selected<PERSON><PERSON><PERSON>,
  onSelectStriker,
  onSelectNonStriker,
  onSelectBowler,
  onConfirm,
  confirmButtonText,
  showBowlerSelection = false,
  showBatsmenSelection = false,
  canConfirm,
  loading = false,
  match,
  currentInnings,
}) => {

  const getExcludedPlayerIds = (forRole: 'striker' | 'nonStriker') => {
    const excluded: string[] = [];
    
    if (forRole === 'striker' && selectedNonStriker) {
      excluded.push(selectedNonStriker.id);
    } else if (forRole === 'nonStriker' && selectedStriker) {
      excluded.push(selectedStriker.id);
    }
    
    return excluded;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      <View style={styles.content}>
        {/* Batsmen Selection */}
        {showBatsmenSelection && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Select Batsmen</Text>
            
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>Striker:</Text>
              <PlayerDropdown
                players={players}
                selectedPlayer={selectedStriker}
                onSelectPlayer={onSelectStriker!}
                placeholder="Select Striker"
                testID="striker-dropdown"
                accessibilityLabel="Select striker"
                excludePlayerIds={getExcludedPlayerIds('striker')}
              />
            </View>

            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>
                Non-Striker{match?.lastManStanding && players.length <= 1 ? ' (Optional - Last Man Standing)' : ':'}
              </Text>
              <PlayerDropdown
                players={players}
                selectedPlayer={selectedNonStriker}
                onSelectPlayer={onSelectNonStriker!}
                placeholder={match?.lastManStanding && players.length <= 1 ? "Select Non-Striker (Optional)" : "Select Non-Striker"}
                testID="non-striker-dropdown"
                accessibilityLabel="Select non-striker"
                excludePlayerIds={getExcludedPlayerIds('nonStriker')}
              />
            </View>

            {/* Selection Summary */}
            <View style={styles.summary}>
              <Text style={styles.summaryText}>
                Striker: {selectedStriker?.name || 'Not selected'}
              </Text>
              <Text style={styles.summaryText}>
                Non-Striker: {selectedNonStriker?.name || 'Not selected'}
              </Text>
            </View>
          </View>
        )}

        {/* Bowler Selection */}
        {showBowlerSelection && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Select Bowler</Text>
            
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>Bowler:</Text>
              <PlayerDropdown
                players={players}
                selectedPlayer={selectedBowler}
                onSelectPlayer={onSelectBowler!}
                placeholder="Select Bowler"
                testID="bowler-dropdown"
                accessibilityLabel="Select bowler"
                filterOutPlayers={false} // All players can bowl
              />
            </View>

            {/* Selection Summary */}
            <View style={styles.summary}>
              <Text style={styles.summaryText}>
                Bowler: {selectedBowler?.name || 'Not selected'}
              </Text>
            </View>
          </View>
        )}

        {/* Confirm Button */}
        <View style={styles.buttonContainer}>
          <Button
            title={confirmButtonText}
            onPress={onConfirm}
            variant="primary"
            size="large"
            fullWidth
            disabled={!canConfirm || loading}
            loading={loading}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    margin: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 4,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
    textAlign: 'center',
  },
  dropdownContainer: {
    marginBottom: 16,
    alignItems: 'center',
  },
  dropdownLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  summary: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
  },
  summaryText: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 4,
    textAlign: 'center',
  },
  buttonContainer: {
    marginTop: 8,
  },
});

export default PlayerSelectionCard;